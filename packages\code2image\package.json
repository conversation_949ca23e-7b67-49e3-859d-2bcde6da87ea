{"name": "koishi-plugin-yesimbot-extension-code2image", "description": "Yes! I'm <PERSON><PERSON>! 代码转图片扩展插件", "version": "0.0.1", "main": "lib/index.js", "typings": "lib/index.d.ts", "homepage": "https://github.com/HydroGest/YesImBot", "files": ["lib", "dist", "README.md"], "scripts": {"build": "tsc && node esbuild.config.mjs", "dev": "tsc -w --preserveWatchOutput", "lint": "eslint . --ext .ts", "clean": "rm -rf lib .turbo tsconfig.tsbuildinfo *.tgz", "pack": "bun pm pack"}, "license": "MIT", "contributors": ["MiaowFISH <<EMAIL>>"], "keywords": ["koishi", "plugin", "yesimbot", "extension"], "repository": {"type": "git", "url": "git+https://github.com/HydroGest/YesImBot.git", "directory": "packages/code2image"}, "dependencies": {"shiki": "^3.8.1"}, "devDependencies": {"koishi": "^4.18.7", "koishi-plugin-puppeteer": "^3.9.0", "koishi-plugin-yesimbot": "workspace:*"}, "peerDependencies": {"koishi": "^4.18.7", "koishi-plugin-puppeteer": "^3.9.0", "koishi-plugin-yesimbot": "workspace:*"}, "koishi": {"description": {"zh": "为 YesImBot 提供代码转图片功能", "en": "Provides code to image conversion for YesImBot"}, "service": {"required": ["yesimbot"]}}}