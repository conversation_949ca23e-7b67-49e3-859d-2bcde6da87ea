name: Build and Package

on:
  push:
    branches: [dev]

jobs:
  build-and-package:
    runs-on: ubuntu-latest
    timeout-minutes: 15

    steps:
      - name: Check out code
        uses: actions/checkout@v4
        with:
          ref: dev
          fetch-depth: 2

      - name: Cache turbo build setup
        uses: actions/cache@v4
        with:
          path: |
            .turbo
            **/node_modules
          key: ${{ runner.os }}-bun-${{ hashFiles('**/package.json') }}
          restore-keys: |
            ${{ runner.os }}-bun-

      - name: Setup Bun
        uses: oven-sh/setup-bun@v2
        with:
          bun-version: 1.2.0

      - name: Install dependencies
        run: bun install

      - name: Build all packages
        run: bun run build

      - name: Create correctly structured tarballs
        run: |
          mkdir -p tarballs
          
          # 为每个包创建正确结构的 tarball
          for dir in packages/*; do
            if [ -d "$dir" ]; then
              pkg=$(basename "$dir")
              echo "Packaging $pkg..."
              
              # 获取包名（从 package.json 中读取）
              pkg_name=$(jq -r '.name' "$dir/package.json")
              
              # 创建临时目录结构（Yarn 要求的格式）
              mkdir -p "temp/$pkg_name"
              
              # 复制构建输出和配置文件
              find "$dir" -mindepth 1 -maxdepth 1 -type d \( -name "lib" -o -name "dist" -o -name "resources" \) -exec cp -r {} "temp/$pkg_name/" \;
              
              # 复制 package.json
              cp "$dir/package.json" "temp/$pkg_name/"
              
              # 创建 tarball（使用包名而不是目录名）
              tar -czf "tarballs/$pkg_name.tgz" -C "temp" "$pkg_name"
              
              # 清理临时目录
              rm -rf "temp/$pkg_name"
            fi
          done
          
          # 验证生成的 tarball 结构
          echo "Verifying tarball structure:"
          for tgz in tarballs/*.tgz; do
            echo "Contents of $(basename $tgz):"
            tar -tzf "$tgz" | head -5
            echo ""
          done

      - name: Upload all artifacts
        uses: actions/upload-artifact@v4
        with:
          name: packages
          path: tarballs
          retention-days: 7

  publish-artifacts:
    runs-on: ubuntu-latest
    needs: build-and-package
    strategy:
      matrix:
        package: [
          "koishi-plugin-yesimbot", 
          "koishi-plugin-yesimbot-extension-code-interpreter",
          "koishi-plugin-yesimbot-extension-code2image",
          "koishi-plugin-yesimbot-extension-favor",
          "koishi-plugin-yesimbot-extension-mcp",
          "koishi-plugin-yesimbot-extension-sticker-manager"
        ]
    
    steps:
      - name: Download packages artifact
        uses: actions/download-artifact@v4
        with:
          name: packages
          path: tarballs

      - name: Verify download
        run: |
          echo "Verifying package ${{ matrix.package }}"
          if [ ! -f "tarballs/${{ matrix.package }}.tgz" ]; then
            echo "Error: ${{ matrix.package }}.tgz not found!"
            exit 1
          fi

      - name: Upload individual package
        uses: actions/upload-artifact@v4
        with:
          name: ${{ matrix.package }}
          path: tarballs/${{ matrix.package }}.tgz
          retention-days: 7
